/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.authservice.constant.SearchingParameterConstants;
import com.tripudiotech.authservice.request.GroupRequest;
import com.tripudiotech.authservice.response.UserGroupResponse;
import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.ConditionKeyword;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.dto.UserGroupInformation;
import com.tripudiotech.securitylib.dto.UserRealmInformationResponse;
import com.tripudiotech.securitylib.dto.request.UserGroupRequest;
import com.tripudiotech.securitylib.dto.response.ErrorResponseDTO;
import com.tripudiotech.securitylib.service.provider.SecurityProviderService;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.ws.rs.core.GenericType;
import java.time.Duration;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.json.JSONObject;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.tripudiotech.authservice.constant.EntityTypeConstants.PERSON_ENTITY_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
@ApplicationScoped
public class UserGroupService {
    private static final String LOG_PREFIX = "[UserGroupService]";
    @Inject
    @RestClient
    EntityServiceClient entityRepository;
    @Inject
    JsonWebToken jsonWebToken;
    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;
    @Inject
    UserService userService;
    @Inject
    ObjectMapper objectMapper;

    static final String USER_GROUP = "UserGroup";

    public EntityWithPermission create(@NonNull String tenantId,
                                       @NonNull GroupRequest request) {
        if (request.getProperties().isEmpty() || !request.getProperties().containsKey(DBConstants.NAME_PROPERTY)) {
            throw new BadRequestException(tenantId, "Missing name properties");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        String authServerGeneratedId;
        List<String> locationSplits;
        try (Response response = securityProviderService
                .createUserGroup(tenantId, UserGroupRequest.builder()
                        .groupType("Group")
                        .name(request.getProperties().get(DBConstants.NAME_PROPERTY).toString())
                        .description(Optional.ofNullable(request.getProperties().get(DBConstants.DESCRIPTION_PROPERTY)).map(Objects::toString)
                                .orElse(null))
                        .build())) {
            if (!Response.Status.Family.familyOf(response.getStatus()).equals(Response.Status.Family.SUCCESSFUL)) {
                response.bufferEntity();
                log.error("{} Failed to create user group in keycloak. Response: {}", LOG_PREFIX, response.readEntity(String.class));
                if (response.getStatus() == 409) {

                    authServerGeneratedId = securityProviderService
                            .searchUserGroup(tenantId, request.getProperties()
                                    .get(DBConstants.NAME_PROPERTY).toString(), 0, 1).get(0).getId();

                    return getById(tenantId, authServerGeneratedId);
                }
                throw new ServiceException(tenantId,
                        BusinessErrorCode.fromHttpStatusCode(response.getStatus()), Optional.ofNullable(response.readEntity(ErrorResponseDTO.class).getErrorMessage())
                        .map(Object::toString).orElseGet(() -> response.readEntity(String.class)));
            }
            authServerGeneratedId = null;
            locationSplits = Optional.ofNullable(response.getHeaderString("location"))
                    .map(location -> location.split("/"))
                    .map(Arrays::asList)
                    .orElse(Collections.emptyList());
        }
        try {
            if (locationSplits.isEmpty()) {
                log.error("{} Unable to get user group id in keycloak", LOG_PREFIX);
                throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR, "Failed to create an User Group");
            }
            authServerGeneratedId = locationSplits.get(locationSplits.size() - 1);
            Map<String, Object> attributes = new HashMap<>(request.getProperties());
            attributes.put(DBConstants.AUTH_ID_PROPERTY, authServerGeneratedId);
            EntityWithPermission createdTeamResponse;
            createdTeamResponse = entityRepository.createEntity(
                            token,
                            tenantId,
                            USER_GROUP,
                            CreateEntityRequest.builder()
                                    .relations(new HashSet<>())
                                    .attributes(attributes)
                                    .build())
                    .await().atMost(Duration.ofMinutes(2))
                    .readEntity(EntityWithPermission.class);

            return new UserGroupResponse(authServerGeneratedId, createdTeamResponse);
        } catch (Exception exception) {
            log.error("Rolling back data, deleting new created group in keycloak. Name: {}", request.getProperties().get(DBConstants.NAME_PROPERTY).toString());
            // rollback data
            Optional.ofNullable(authServerGeneratedId).filter(StringUtils::isNoneBlank)
                    .ifPresent(generatedId -> {
                        securityProviderServiceFactory.getDefaultAuthenticateService().removeUserGroup(tenantId, generatedId);
                        log.info("Removed user group key cloak id: {}", generatedId);
                    });
            throw exception;
        }
    }

    public EntityWithPermission update(String tenantId, String entityId, GroupRequest request) {
        if (request.getProperties() == null || request.getProperties().isEmpty()) {
            throw new BadRequestException(tenantId, "The required field is empty");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission entity =
                entityRepository.getEntityDetail(token, tenantId, USER_GROUP, entityId)
                        .await().atMost(Duration.ofMinutes(2))
                        .readEntity(EntityWithPermission.class);

        String authId = Optional.ofNullable(entity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                    log.error("{} Could not find authId property for group Id {}", LOG_PREFIX, entityId);
                    return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                });
        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        UserGroupInformation existingUserGroup = securityProviderService.getUserGroupById(tenantId, authId);

        EntityWithPermission updatedEntity = entityRepository.updateEntity(token, tenantId, entityId,
                        UpdateEntityRequest.builder()
                                .attributes(request.getProperties())
                                .build())
                .await().indefinitely()
                .readEntity(EntityWithPermission.class);
        // Do update group name in keycloak
        try {
            String newGroupName = Optional.of(request.getProperties())
                    .map(value -> value.get(DBConstants.NAME_PROPERTY))
                    .map(Object::toString).filter(StringUtils::isNoneBlank).orElse(null);
            if (!StringUtils.isBlank(newGroupName) &&
                    !newGroupName.equals(existingUserGroup.getName())) {
                securityProviderService.updateUserGroup(tenantId, authId, newGroupName);
            } else {
                log.info("{} Properties.name is empty or the same with old group name, ignore update in keycloak", LOG_PREFIX);
            }
            return updatedEntity;
        } catch (Exception e) {
            log.error("{} Failed to updated group name in keycloak, rolling back the data in neo4j", LOG_PREFIX, e);
            Map<String, Object> originalProperties = entity.getProperties();
            entityRepository.updateEntity(token, tenantId, entityId,
                            UpdateEntityRequest.builder()
                                    .attributes(originalProperties)
                                    .build())
                    .await().atMost(Duration.ofMinutes(2))
                    .readEntity(EntityWithPermission.class);
            throw e;
        }
    }

    public PageResponse<EntityWithPermission> searchGroups(
            @NonNull String tenantId,
            String query,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getAll(token, tenantId, USER_GROUP, offset, limit, query, null, null, null, null)
                .map(response ->
                        response.readEntity(new GenericType<PageResponse<EntityWithPermission>>() {
                        }))
                .await().indefinitely();

    }

    public void removeGroup(
            @NonNull String tenantId,
            @NonNull String groupId) {

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission teamEntityWithPermission =
                entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                        .await().indefinitely()
                        .readEntity(EntityWithPermission.class);
        String authId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).orElse(null);
        entityRepository.deleteEntity(token, tenantId, groupId).await().indefinitely();

        if (!StringUtils.isBlank(authId)) {
            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
            securityProviderService.removeUserGroup(tenantId, authId);
        } else {
            log.info("{} Could not found authId. Skip delete UserGroup in keycloak", LOG_PREFIX);
        }
    }

    public Uni<Void> addUserToGroupAsync(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        if (userEmail.isBlank()) {
            throw new BadRequestException(tenantId, "User email must be non-blank");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                .map(response -> response.readEntity(EntityWithPermission.class))
                .flatMap(entity -> {
                    String authId = Optional.ofNullable(entity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                            .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                                log.error("{} Could not find authId property for group Id {}", LOG_PREFIX, groupId);
                                return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                            });

                    return userService.getUserEntityByEmailAsync(tenantId, userEmail)
                            .map(addedUser -> {
                                SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
                                MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
                                queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
                                List<UserRealmInformationResponse> userResponseList = securityProviderService.searchUsers(tenantId, queryParams);
                                if (userResponseList.isEmpty()) {
                                    throw new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail);
                                }
                                UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                                        .filter(data -> data.getEmail().equals(userEmail))
                                        .findFirst()
                                        .orElseThrow(() -> new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail));

                                // Complete add user to group in keycloak
                                securityProviderService.addUserToGroup(tenantId, authId, userRealmInformationResponse.getId());
                                log.info("{} Add user to group in keycloak success. Email: {}, GroupId: {}", LOG_PREFIX, userEmail, authId);

                                return null; // Return void
                            });
                })
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .replaceWithVoid();
    }

    public void addUserToGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        addUserToGroupAsync(tenantId, groupId, userEmail)
                .await().atMost(Duration.ofMinutes(2));
    }

    public void removeUserFromGroup(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull String userEmail) {
        if (userEmail.isBlank()) {
            throw new BadRequestException(tenantId, "User email must be non-blank");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission entity =
                entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                        .await().atMost(Duration.ofMinutes(2))
                        .readEntity(EntityWithPermission.class);

        String authId = Optional.ofNullable(entity.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                    log.error("{} Could not find authServerId property for group Id {}", LOG_PREFIX, groupId);
                    return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                });

        EntityWithPermission userInfo = userService.getUserEntityByEmail(tenantId, userEmail);

        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
        queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
        List<UserRealmInformationResponse> userResponseList = securityProviderServiceFactory.getDefaultAuthenticateService().searchUsers(tenantId, queryParams);
        if (userResponseList.isEmpty()) {
            throw new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail);
        }

        UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                .filter(data -> data.getEmail().equals(userEmail))
                .findFirst()
                .orElseThrow(() -> new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail));

        // Complete add user to group in keycloak
        securityProviderService.removeUserToTheGroup(tenantId, authId, userRealmInformationResponse.getId());
        log.info("{} Remove user to group in keycloak success. Email: {}, GroupId: {}", LOG_PREFIX, userEmail, authId);
        try {
            entityRepository.deleteRelation(
                    token, tenantId, userInfo.getId(), DBConstants.RELATION_WORKS_FOR, groupId).await().indefinitely();
            log.info("{} Delete WORKS_FOR relationship success. UserId: {}, GroupId: {}", LOG_PREFIX,
                    userInfo.getId(), groupId);
        } catch (Exception exception) {
            log.error("{} Failed to delete relation between group and user. GroupId: {}, UserId: {}", LOG_PREFIX,
                    groupId, userInfo.getId(), exception);
            securityProviderService.addUserToGroup(tenantId, authId, userRealmInformationResponse.getId());
            throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
        }
    }

    @SuppressWarnings("unchecked")
    public PageResponse<?> getUsersInGroupBy(
            @NonNull String tenantId,
            @NonNull String groupId,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getEntitiesUnderRelationType(
                token,
                tenantId,
                offset,
                limit,
                groupId,
                DBConstants.RELATION_WORKS_FOR,
                PERSON_ENTITY_TYPE,
                true
        ).await().atMost(Duration.ofMinutes(2)).readEntity(PageResponse.class);
    }

    /**
     * Find a group by name, create it if it doesn't exist
     * @param tenantId the tenant ID
     * @param groupName the name of the group to find or create
     * @return the group entity
     */
    public EntityWithPermission findOrCreateGroupByName(@NonNull String tenantId, @NonNull String groupName) {
        if (StringUtils.isBlank(groupName)) {
            throw new BadRequestException(tenantId, "Group name cannot be empty or blank");
        }
        // Search for existing group by name
        JSONObject jsonQuery = new JSONObject()
                .put(
                        ConditionKeyword.EXACT.getValue(),
                        new JSONObject()
                                .put(String.format("%s", DBConstants.NAME_PROPERTY), groupName));

        PageResponse<EntityWithPermission> searchResult = searchGroups(tenantId, jsonQuery.toString(), 0, 1);

        log.info("{} Search result: {} ", LOG_PREFIX, searchResult.getData() );

        // If group exists, return it
        if (searchResult.getData() != null && !searchResult.getData().isEmpty()) {

            log.info("{} Group '{}' found, returning existing group", LOG_PREFIX, groupName);

            return searchResult.getData().get(0);
        }

        // Group doesn't exist, create it
        log.info("{} Group '{}' not found, creating new group", LOG_PREFIX, groupName);

        Map<String, Object> groupProperties = new HashMap<>();
        groupProperties.put(DBConstants.NAME_PROPERTY, groupName);
        groupProperties.put(DBConstants.DESCRIPTION_PROPERTY, "Auto-created group for user assignment");

        GroupRequest groupRequest = GroupRequest.builder()
                .properties(groupProperties)
                .build();

        return create(tenantId, groupRequest);
    }

    /**
     * Get a group by ID
     * @param tenantId the tenant ID
     * @param groupId the ID of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public EntityWithPermission getById(@NonNull String tenantId, @NonNull String groupId) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        try {
            return entityRepository.getEntityDetail(token, tenantId, USER_GROUP, groupId)
                    .await().atMost(Duration.ofMinutes(2))
                    .readEntity(EntityWithPermission.class);
        } catch (Exception e) {
            throw new RecordNotFoundException(tenantId, "Group not found with ID: " + groupId);
        }
    }

    /**
     * Find a group by name (without auto-creation)
     * @param tenantId the tenant ID
     * @param groupName the name of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public EntityWithPermission findGroupByName(@NonNull String tenantId, @NonNull String groupName) {
        if (StringUtils.isBlank(groupName)) {
            throw new BadRequestException(tenantId, "Group name cannot be empty or blank");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        // Search for existing group by name
        String query = new JSONObject().put("$exact", new JSONObject().put(DBConstants.NAME_PROPERTY, groupName)).toString();
        PageResponse<?> searchResult = entityRepository.getAll(token, tenantId, USER_GROUP, 0, 1, query, null, null, null, null)
                .await().atMost(Duration.ofMinutes(2))
                .readEntity(PageResponse.class);

        // If group exists, return it
        if (searchResult.getData() != null && !searchResult.getData().isEmpty()) {
            Object firstResult = searchResult.getData().get(0);
            if (firstResult instanceof List<?> list && !list.isEmpty()) {
                return objectMapper.convertValue(list.get(0), EntityWithPermission.class);
            }
        }

        throw new RecordNotFoundException(tenantId, "Group not found with name: " + groupName);
    }

    /**
     * Find a group by external ID
     * @param tenantId the tenant ID
     * @param externalId the external ID of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public EntityWithPermission findGroupByExternalId(@NonNull String tenantId, @NonNull String externalId) {
        if (StringUtils.isBlank(externalId)) {
            throw new BadRequestException(tenantId, "External ID cannot be empty or blank");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        // Search for existing group by external ID
        String query = new JSONObject().put("$exact", new JSONObject().put("externalId", externalId)).toString();
        PageResponse<?> searchResult = entityRepository.getAll(token, tenantId, USER_GROUP, 0, 1, query, null, null, null, null)
                .await().atMost(Duration.ofMinutes(2))
                .readEntity(PageResponse.class);

        // If group exists, return it
        if (searchResult.getData() != null && !searchResult.getData().isEmpty()) {
            Object firstResult = searchResult.getData().get(0);
            if (firstResult instanceof List<?> list && !list.isEmpty()) {
                return objectMapper.convertValue(list.get(0), EntityWithPermission.class);
            }
        }

        throw new RecordNotFoundException(tenantId, "Group not found with external ID: " + externalId);
    }

    /**
     * Find a group by code
     * @param tenantId the tenant ID
     * @param code the code of the group to find
     * @return the group entity
     * @throws RecordNotFoundException if group is not found
     */
    public EntityWithPermission findGroupByCode(@NonNull String tenantId, @NonNull String code) {
        if (StringUtils.isBlank(code)) {
            throw new BadRequestException(tenantId, "Group code cannot be empty or blank");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());

        // Search for existing group by code
        String query = new JSONObject().put("$exact", new JSONObject().put("code", code)).toString();
        PageResponse<?> searchResult = entityRepository.getAll(token, tenantId, USER_GROUP, 0, 1, query, null, null, null, null)
                .await().atMost(Duration.ofMinutes(2))
                .readEntity(PageResponse.class);

        // If group exists, return it
        if (searchResult.getData() != null && !searchResult.getData().isEmpty()) {
            Object firstResult = searchResult.getData().get(0);
            if (firstResult instanceof List<?> list && !list.isEmpty()) {
                return objectMapper.convertValue(list.get(0), EntityWithPermission.class);
            }
        }

        throw new RecordNotFoundException(tenantId, "Group not found with code: " + code);
    }
}
