/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.authservice.constant.SearchingParameterConstants;
import com.tripudiotech.authservice.request.EntityUnderCompanyRequest;
import com.tripudiotech.authservice.request.TeamRequest;
import com.tripudiotech.authservice.request.TeamUpdateRequest;
import com.tripudiotech.authservice.response.TeamResponse;
import com.tripudiotech.base.client.EntityServiceClient;
import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.request.UpdateEntityRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.util.TokenUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.dto.UserGroupInformation;
import com.tripudiotech.securitylib.dto.UserRealmInformationResponse;
import com.tripudiotech.securitylib.dto.request.UserGroupRequest;
import com.tripudiotech.securitylib.dto.response.ErrorResponseDTO;
import com.tripudiotech.securitylib.service.provider.SecurityProviderService;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.tripudiotech.authservice.constant.EntityTypeConstants.TEAM_ENTITY_TYPE;

@ApplicationScoped
@Slf4j
public class TeamService {

    private static final String LOG_PREFIX = "[TeamService]";

    @Inject
    @RestClient
    EntityServiceClient entityRepository;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ObjectMapper objectMapper;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    UserService userService;

    public EntityWithPermission create(@NonNull String tenantId,
                                       @NonNull String companyId,
                                       @NonNull TeamRequest teamRequest) {
        if (teamRequest.getProperties().isEmpty() || !teamRequest.getProperties().containsKey(DBConstants.NAME_PROPERTY)) {
            throw new BadRequestException(tenantId, "Missing name properties");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityUnderCompanyRequest entityUnderCompanyRequest = new EntityUnderCompanyRequest(companyId, teamRequest.getProperties());
        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        Response response = securityProviderService
                .createUserGroup(tenantId, UserGroupRequest.builder()
                        .companyId(companyId)
                        .groupType("Team")
                        .name(teamRequest.getProperties().get(DBConstants.NAME_PROPERTY).toString())
                        .description(Optional.ofNullable(teamRequest.getProperties().get(DBConstants.DESCRIPTION_PROPERTY)).map(Objects::toString)
                                .orElse(null))
                        .build());
        if (!Response.Status.Family.familyOf(response.getStatus()).equals(Response.Status.Family.SUCCESSFUL)) {
            response.bufferEntity();
            throw new ServiceException(tenantId,
                    BusinessErrorCode.fromHttpStatusCode(response.getStatus()), Optional.ofNullable(response.readEntity(ErrorResponseDTO.class).getErrorMessage())
                    .map(Object::toString).orElseGet(() -> response.readEntity(String.class)));
        }
        String authServerGeneratedId = null;
        List<String> locationSplits = Optional.ofNullable(response.getHeaderString("location"))
                .map(location -> location.split("/"))
                .map(Arrays::asList)
                .orElse(Collections.emptyList());
        try {
            if (locationSplits.isEmpty()) {
                log.error("{} Unable to get user group id in keycloak", LOG_PREFIX);
                throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR, "Fail to get generated user group id");
            }
            authServerGeneratedId = locationSplits.get(locationSplits.size() - 1);
            Map<String, Object> attributes = new HashMap<>(entityUnderCompanyRequest.getAttributes());
            attributes.put(DBConstants.AUTH_ID_PROPERTY, authServerGeneratedId);
            EntityWithPermission createdTeamResponse = entityRepository.createEntity(
                            token,
                            tenantId,
                            TEAM_ENTITY_TYPE,
                            CreateEntityRequest.builder()
                                    .relations(entityUnderCompanyRequest.getRelations())
                                    .attributes(attributes)
                                    .build())
                    .await().atMost(Duration.ofMinutes(2))
                    .readEntity(EntityWithPermission.class);

            return new TeamResponse(authServerGeneratedId, companyId, createdTeamResponse);
        } catch (Exception exception) {
            log.error("Rolling back data, deleting new created group in keycloak. Name: {}", teamRequest.getProperties().get(DBConstants.NAME_PROPERTY).toString());
            // rollback data
            Optional.ofNullable(authServerGeneratedId).filter(StringUtils::isNoneBlank)
                    .ifPresent(generatedId -> {
                        securityProviderServiceFactory.getDefaultAuthenticateService().removeUserGroup(tenantId, generatedId);
                        log.info("Removed user group key cloak id: {}", generatedId);
                    });
            throw exception;
        }
    }

    public EntityWithPermission update(String tenantId, String teamId, TeamUpdateRequest request) {
        if (request.getProperties() == null || request.getProperties().isEmpty()) {
            throw new BadRequestException(tenantId, "The required field is empty");
        }

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission teamEntityWithPermission =
                entityRepository.getEntityDetail(token, tenantId, TEAM_ENTITY_TYPE, teamId)
                        .await().atMost(Duration.ofMinutes(2))
                        .readEntity(EntityWithPermission.class);

        String authServerId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                    log.error("{} Could not find authServerId property for team Id {}", LOG_PREFIX, teamId);
                    return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                });
        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        UserGroupInformation existingUserGroup = securityProviderService.getUserGroupById(tenantId, authServerId);

        EntityWithPermission updatedEntity = entityRepository.updateEntity(token, tenantId, teamId,
                        UpdateEntityRequest.builder()
                                .attributes(request.getProperties())
                                .build())
                .await().atMost(Duration.ofMinutes(2))
                .readEntity(EntityWithPermission.class);
        // Do update group name in keycloak
        try {
            String newGroupName = Optional.ofNullable(request.getProperties())
                    .map(value -> value.get(DBConstants.NAME_PROPERTY))
                    .map(Object::toString).filter(StringUtils::isNoneBlank).orElse(null);
            if (!StringUtils.isBlank(newGroupName) &&
                    !newGroupName.equals(existingUserGroup.getName())) {
                securityProviderService.updateUserGroup(tenantId, authServerId, newGroupName);
            } else {
                log.info("{} Properties.name is empty or the same with old group name, ignore update in keycloak", LOG_PREFIX);
            }
            return updatedEntity;
        } catch (Exception e) {
            log.error("{} Failed to updated group name in keycloak, rolling back the data in neo4j", LOG_PREFIX);
            Map<String, Object> teamPropertiesBeforeUpdated = teamEntityWithPermission.getProperties();
            entityRepository.updateEntity(token, tenantId, teamId,
                            UpdateEntityRequest.builder()
                                    .attributes(teamPropertiesBeforeUpdated)
                                    .build())
                    .await().atMost(Duration.ofMinutes(2))
                    .readEntity(EntityWithPermission.class);
            throw e;
        }
    }

    public PageResponse<?> searchTeams(
            @NonNull String tenantId,
            String query,
            @NonNull Integer offset,
            @NonNull Integer limit
    ) {
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        return entityRepository.getAll(token, tenantId, TEAM_ENTITY_TYPE, offset, limit, query, null, null, null, null)
                .await().atMost(Duration.ofMinutes(2))
                .readEntity(PageResponse.class);

    }

    public void removeTeam(
            @NonNull String tenantId,
            @NonNull String teamId) {

        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission teamEntityWithPermission =
                entityRepository.getEntityDetail(token, tenantId, TEAM_ENTITY_TYPE, teamId)
                        .await().atMost(Duration.ofMinutes(2))
                        .readEntity(EntityWithPermission.class);
        String authServerId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).orElse(null);
        entityRepository.deleteEntity(token, tenantId, teamId);

        if (!StringUtils.isBlank(authServerId)) {
            SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
            securityProviderService.removeUserGroup(tenantId, authServerId);
        } else {
            log.info("{} Could not found authServerId. Skip delete TEAM in keycloak", LOG_PREFIX);
        }
    }

    public void addUserToGroup(
            @NonNull String tenantId,
            @NonNull String teamId,
            @NonNull String userEmail) {
        if (userEmail.isBlank()) {
            throw new BadRequestException(tenantId, "User email must be non-blank");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission teamEntityWithPermission =
                entityRepository.getEntityDetail(token, tenantId, TEAM_ENTITY_TYPE, teamId)
                        .await().atMost(Duration.ofMinutes(2))
                        .readEntity(EntityWithPermission.class);
        String authUserGroupServerId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                    log.error("{} Could not find authServerId property for team Id {}", LOG_PREFIX, teamId);
                    return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                });

        EntityWithPermission userEntityWithPermission = userService.getUserEntityByEmail(tenantId, userEmail);

        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
        queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
        List<UserRealmInformationResponse> userResponseList = securityProviderServiceFactory.getDefaultAuthenticateService().searchUsers(tenantId, queryParams);
        if (userResponseList.isEmpty()) {
            throw new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail);
        }
        UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                .filter(data -> data.getEmail().equals(userEmail))
                .findFirst()
                .orElseThrow(() -> new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail));

        // Complete add user to group in keycloak
        securityProviderService.addUserToGroup(tenantId, authUserGroupServerId, userRealmInformationResponse.getId());
        log.info("{} Add user to group in keycloak success. Email: {}, GroupId: {}", LOG_PREFIX, userEmail, authUserGroupServerId);
        try {
            entityRepository.createRelationWithProperties(
                            token, tenantId, userEntityWithPermission.getId(), DBConstants.RELATION_WORKS_FOR, teamId, EntityServiceClient.RelationRequest.builder().build())
                    .await().atMost(Duration.ofMinutes(2));
            log.info("{} Insert WORKS_FOR relationship success. UserId: {}, TeamId: {}", LOG_PREFIX, userEntityWithPermission.getId(), teamId);
        } catch (Exception exception) {
            log.error("{} Failed to create relation between team and user. TeamId: {}, UserId: {}", LOG_PREFIX, teamId, userEntityWithPermission.getId(), exception);
            securityProviderService.removeUserToTheGroup(tenantId, authUserGroupServerId, userRealmInformationResponse.getId());
            throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
        }
    }

    public void removeUserToGroup(
            @NonNull String tenantId,
            @NonNull String teamId,
            @NonNull String userEmail) {
        if (userEmail.isBlank()) {
            throw new BadRequestException(tenantId, "User email must be non-blank");
        }
        String token = TokenUtils.addBearerPrefix(jsonWebToken.getRawToken());
        EntityWithPermission teamEntityWithPermission =
                entityRepository.getEntityDetail(token, tenantId, TEAM_ENTITY_TYPE, teamId)
                        .await().atMost(Duration.ofMinutes(2))
                        .readEntity(EntityWithPermission.class);
        String authUserGroupServerId = Optional.ofNullable(teamEntityWithPermission.getProperties().get(DBConstants.AUTH_ID_PROPERTY))
                .map(Object::toString).filter(StringUtils::isNoneBlank).orElseThrow(() -> {
                    log.error("{} Could not find authServerId property for team Id {}", LOG_PREFIX, teamId);
                    return new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
                });

        EntityWithPermission userEntityWithPermission = userService.getUserEntityByEmail(tenantId, userEmail);

        SecurityProviderService securityProviderService = securityProviderServiceFactory.getDefaultAuthenticateService();
        MultivaluedMap<String, String> queryParams = new MultivaluedHashMap<>();
        queryParams.put(SearchingParameterConstants.EMAIL, List.of(userEmail.trim()));
        List<UserRealmInformationResponse> userResponseList = securityProviderServiceFactory.getDefaultAuthenticateService().searchUsers(tenantId, queryParams);
        if (userResponseList.isEmpty()) {
            throw new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail);
        }
        UserRealmInformationResponse userRealmInformationResponse = userResponseList.stream()
                .filter(data -> data.getEmail().equals(userEmail))
                .findFirst()
                .orElseThrow(() -> new RecordNotFoundException(tenantId, "Could not found user with email " + userEmail));


        // Complete add user to group in keycloak
        securityProviderService.removeUserToTheGroup(tenantId, authUserGroupServerId, userRealmInformationResponse.getId());
        log.info("{} Remove user to group in keycloak success. Email: {}, GroupId: {}", LOG_PREFIX, userEmail, authUserGroupServerId);
        try {
            entityRepository.deleteRelation(
                    token, tenantId, userEntityWithPermission.getId(), DBConstants.RELATION_WORKS_FOR, teamId).await().atMost(Duration.ofMinutes(2));
            log.info("{} Delete WORKS_FOR relationship success. UserId: {}, TeamId: {}", LOG_PREFIX, userEntityWithPermission.getId(), teamId);
        } catch (Exception exception) {
            log.error("{} Failed to delete relation between team and user. TeamId: {}, UserId: {}", LOG_PREFIX, teamId, userEntityWithPermission.getId(), exception);
            securityProviderService.addUserToGroup(tenantId, authUserGroupServerId, userRealmInformationResponse.getId());
            throw new ServiceException(tenantId, BusinessErrorCode.SERVICE_INTERNAL_ERROR);
        }
    }
}
