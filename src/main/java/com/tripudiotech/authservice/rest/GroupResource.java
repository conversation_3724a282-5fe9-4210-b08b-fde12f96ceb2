/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.request.GroupRequest;
import com.tripudiotech.authservice.service.UserGroupService;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.constant.RoleConstant;
import io.quarkus.security.Authenticated;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/**
 * <AUTHOR>
 */
@Path("/group")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityRequirement(name = "apiToken")
public class GroupResource extends RestResource {
    @Inject
    UserGroupService userGroupService;

    @POST
    @Operation(summary = "Create a group")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Response createGroup(GroupRequest request) {
        return Response.ok(userGroupService.create(this.tenantId, request)).build();
    }

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @APIResponseSchema(value = PageResponse.class)
    @Operation(summary = "Search groups by group name")
    public Response searchUserGroups(@QueryParam(RequestConstants.OFFSET_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_OFFSET) @Parameter(description = "The page number") Integer offset,
                                     @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_LIMIT) @Parameter(description = "Maximum results size (defaults to 100)") Integer limit,
                                     @QueryParam(RequestConstants.QUERY_REQUEST_PARAM) String query) {
        return Response.ok(userGroupService.searchGroups(tenantId, query, offset, limit)).build();
    }


    @PUT
    @Operation(summary = "Update a Group information")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}")
    public Response update(@PathParam("id") String entityId,
                           GroupRequest request) {
        return Response.ok(userGroupService.update(tenantId, entityId, request))
                .build();
    }

    @DELETE
    @Operation(summary = "Delete a Group")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}")
    public Response removeGroup(@PathParam("id") String entityId) {
        userGroupService.removeGroup(tenantId, entityId);
        return Response.noContent()
                .build();
    }

    @POST
    @Operation(summary = "Add an User into a Group")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}/user/{userEmail}")
    public Response addUserToGroup(@PathParam("id") String entityId,
                                   @PathParam("userEmail") String email) {
        userGroupService.addUserToGroup(tenantId, entityId, email);
        return Response.status(Response.Status.OK)
                .build();
    }

    @DELETE
    @Operation(summary = "Remove an User out of the Group")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}/user/{userEmail}")
    public Response removeUserFromGroup(@PathParam("id") String entityId,
                                        @PathParam("userEmail") String email) {
        userGroupService.removeUserFromGroup(tenantId, entityId, email);
        return Response.status(Response.Status.OK)
                .build();
    }

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @APIResponseSchema(value = PageResponse.class)
    @Path("/{id}/user")
    @Operation(summary = "Users by group id")
    public Response getUserByGroupId(
            @QueryParam(RequestConstants.OFFSET_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_OFFSET) @Parameter(description = "The page number") Integer offset,
            @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_LIMIT) @Parameter(description = "Maximum results size (defaults to 100)") Integer limit,
            @PathParam("id") String groupId
    ) {
        return Response.ok(userGroupService.getUsersInGroupBy(tenantId, groupId, offset, limit)).build();
    }
}
